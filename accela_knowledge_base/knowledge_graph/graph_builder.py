"""
Knowledge graph builder for Accela implementations
Creates and manages the graph-based knowledge representation
"""

import json
import networkx as nx
from typing import Dict, List, Any, Optional

from ..core.config import Config
from ..core.exceptions import GraphBuildError
from ..core.logging import LoggerMixin
from ..data.metadata_extractor import MetadataExtractor


class AccelaKnowledgeGraph(LoggerMixin):
    """Knowledge graph for Accela implementations with naming convention support"""
    
    def __init__(self, config: Config):
        self.config = config
        self.graph = nx.MultiDiGraph()
        self.metadata_extractor = MetadataExtractor(config)
    
    def build_from_metadata(self) -> None:
        """Build knowledge graph from extracted metadata"""
        self.logger.info("Building knowledge graph from metadata")
        
        try:
            # Load metadata
            scripts_data = self.metadata_extractor.load_metadata()
            
            if not scripts_data:
                self.logger.warning("No metadata found, extracting from scripts")
                scripts = self.metadata_extractor.extract_all()
                scripts_data = self._convert_scripts_to_dict(scripts)
            
            # Build graph nodes and edges
            self._build_graph_nodes(scripts_data)
            self._build_graph_edges(scripts_data)
            self._calculate_similarities()
            
            stats = self.get_stats()
            self.logger.info(f"Graph built successfully: {stats['nodes']} nodes, {stats['edges']} edges")
            
        except Exception as e:
            self.logger.error(f"Failed to build knowledge graph: {e}")
            raise GraphBuildError(f"Graph building failed: {str(e)}")
    
    def _convert_scripts_to_dict(self, scripts) -> List[Dict]:
        """Convert ScriptMetadata objects to dictionaries"""
        scripts_data = []
        for script in scripts:
            script_dict = {
                'file_path': script.file_path,
                'county': script.county,
                'script_type': script.script_type,
                'naming_convention': {
                    'event_prefix': script.naming_convention.event_prefix,
                    'module': script.naming_convention.module,
                    'application_type': script.naming_convention.application_type,
                    'sub_type': script.naming_convention.sub_type,
                    'category': script.naming_convention.category,
                    'is_wildcard': script.naming_convention.is_wildcard,
                    'raw_filename': script.naming_convention.raw_filename
                },
                'functions': script.functions,
                'dependencies': script.dependencies,
                'complexity': script.complexity,
                'documentation_quality': script.documentation_quality,
                'module': script.module,
                'app_type': script.app_type
            }
            scripts_data.append(script_dict)
        return scripts_data
    
    def _build_graph_nodes(self, scripts_data: List[Dict]) -> None:
        """Build graph nodes from scripts data"""
        
        counties = set()
        event_prefixes = set()
        modules = set()
        app_types = set()
        functions = set()
        
        for script in scripts_data:
            county = script['county']
            counties.add(county)
            
            # Add county node
            if not self.graph.has_node(f"county:{county}"):
                self.graph.add_node(f"county:{county}", 
                                  type="county", 
                                  name=county,
                                  script_count=0)
            
            # Update county stats
            self.graph.nodes[f"county:{county}"]['script_count'] += 1
            
            # Extract naming convention components
            naming = script.get('naming_convention', {})
            event_prefix = naming.get('event_prefix')
            module = naming.get('module')
            app_type = naming.get('application_type')
            
            # Add script node
            script_id = f"script:{script['file_path']}"
            self.graph.add_node(script_id,
                              type="script",
                              county=county,
                              script_type=script['script_type'],
                              event_prefix=event_prefix,
                              module=module,
                              application_type=app_type,
                              complexity=script['complexity'],
                              doc_quality=script['documentation_quality'],
                              functions=script.get('functions', []))
            
            # Add event prefix nodes
            if event_prefix:
                event_prefixes.add(event_prefix)
                prefix_id = f"event_prefix:{event_prefix}"
                
                if not self.graph.has_node(prefix_id):
                    self.graph.add_node(prefix_id, 
                                      type="event_prefix", 
                                      name=event_prefix,
                                      description=self.config.event_prefixes.get(event_prefix, f"Unknown: {event_prefix}"),
                                      usage_count=0)
                
                self.graph.nodes[prefix_id]['usage_count'] += 1
            
            # Add module nodes
            if module:
                modules.add(module)
                module_id = f"module:{module}"
                
                if not self.graph.has_node(module_id):
                    self.graph.add_node(module_id, 
                                      type="module", 
                                      name=module,
                                      usage_count=0)
                
                self.graph.nodes[module_id]['usage_count'] += 1
            
            # Add application type nodes
            if app_type:
                app_types.add(app_type)
                app_type_id = f"app_type:{app_type}"
                
                if not self.graph.has_node(app_type_id):
                    self.graph.add_node(app_type_id, 
                                      type="app_type", 
                                      name=app_type,
                                      usage_count=0)
                
                self.graph.nodes[app_type_id]['usage_count'] += 1
            
            # Add function nodes
            for func in script.get('functions', []):
                functions.add(func)
                func_id = f"function:{func}"
                
                if not self.graph.has_node(func_id):
                    self.graph.add_node(func_id, 
                                      type="function", 
                                      name=func,
                                      usage_count=0)
                
                self.graph.nodes[func_id]['usage_count'] += 1
        
        self.logger.debug(f"Created nodes: {len(counties)} counties, {len(event_prefixes)} prefixes, "
                         f"{len(modules)} modules, {len(app_types)} app types, {len(functions)} functions")
    
    def _build_graph_edges(self, scripts_data: List[Dict]) -> None:
        """Build graph edges from scripts data"""
        
        for script in scripts_data:
            county = script['county']
            script_id = f"script:{script['file_path']}"
            county_id = f"county:{county}"
            
            # Connect county to script
            self.graph.add_edge(county_id, script_id, relationship="contains")
            
            # Connect script to naming convention components
            naming = script.get('naming_convention', {})
            
            if naming.get('event_prefix'):
                prefix_id = f"event_prefix:{naming['event_prefix']}"
                self.graph.add_edge(script_id, prefix_id, relationship="uses_event_prefix")
            
            if naming.get('module'):
                module_id = f"module:{naming['module']}"
                self.graph.add_edge(script_id, module_id, relationship="targets_module")
            
            if naming.get('application_type'):
                app_type_id = f"app_type:{naming['application_type']}"
                self.graph.add_edge(script_id, app_type_id, relationship="handles_app_type")
            
            # Connect script to functions
            for func in script.get('functions', []):
                func_id = f"function:{func}"
                self.graph.add_edge(script_id, func_id, relationship="implements")
    
    def _calculate_similarities(self) -> None:
        """Calculate similarity relationships between nodes"""
        
        # County similarities based on shared components
        counties = [n for n in self.graph.nodes() if n.startswith("county:")]
        
        for i, county1 in enumerate(counties):
            for county2 in counties[i+1:]:
                similarity = self._calculate_county_similarity(county1, county2)
                if similarity > self.config.similarity_threshold:
                    self.graph.add_edge(county1, county2, 
                                      relationship="similar_to", 
                                      weight=similarity)
    
    def _calculate_county_similarity(self, county1: str, county2: str) -> float:
        """Calculate similarity between two counties"""
        
        # Get components for each county
        county1_components = self._get_county_components(county1)
        county2_components = self._get_county_components(county2)
        
        # Calculate Jaccard similarity for each component type
        similarities = []
        
        for component_type in ['event_prefixes', 'modules', 'app_types', 'functions']:
            set1 = county1_components.get(component_type, set())
            set2 = county2_components.get(component_type, set())
            
            if set1 or set2:
                intersection = len(set1.intersection(set2))
                union = len(set1.union(set2))
                similarity = intersection / union if union > 0 else 0.0
                similarities.append(similarity)
        
        # Return weighted average
        if similarities:
            weights = [0.3, 0.25, 0.25, 0.2]  # event_prefixes, modules, app_types, functions
            return sum(s * w for s, w in zip(similarities, weights))
        
        return 0.0
    
    def _get_county_components(self, county_node: str) -> Dict[str, set]:
        """Get all components for a county"""
        components = {
            'event_prefixes': set(),
            'modules': set(),
            'app_types': set(),
            'functions': set()
        }
        
        # Get all scripts for this county
        for script_node in self.graph.successors(county_node):
            if script_node.startswith("script:"):
                script_data = self.graph.nodes[script_node]
                
                if script_data.get('event_prefix'):
                    components['event_prefixes'].add(script_data['event_prefix'])
                
                if script_data.get('module'):
                    components['modules'].add(script_data['module'])
                
                if script_data.get('application_type'):
                    components['app_types'].add(script_data['application_type'])
                
                for func in script_data.get('functions', []):
                    components['functions'].add(func)
        
        return components
    
    def get_stats(self) -> Dict[str, Any]:
        """Get knowledge graph statistics"""
        
        node_types = {}
        for node in self.graph.nodes():
            node_type = self.graph.nodes[node].get('type', 'unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        return {
            'nodes': self.graph.number_of_nodes(),
            'edges': self.graph.number_of_edges(),
            'counties': node_types.get('county', 0),
            'scripts': node_types.get('script', 0),
            'functions': node_types.get('function', 0),
            'event_prefixes': node_types.get('event_prefix', 0),
            'modules': node_types.get('module', 0),
            'app_types': node_types.get('app_type', 0),
            'density': nx.density(self.graph),
            'node_types': node_types
        }
    
    def export_graph(self, filename: Optional[str] = None) -> None:
        """Export graph for visualization"""
        if filename is None:
            filename = self.config.graph_export_file
        
        try:
            nx.write_gexf(self.graph, filename)
            self.logger.info(f"Graph exported to {filename}")
        except Exception as e:
            self.logger.error(f"Failed to export graph: {e}")
            raise
