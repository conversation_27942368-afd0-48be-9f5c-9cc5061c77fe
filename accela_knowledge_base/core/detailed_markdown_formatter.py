"""
Detailed markdown formatter for actual code analysis and differences
"""

from typing import Dict, List
from .logging import LoggerMixin
from .detailed_code_analyzer import DetailedCodeAnalyzer


class DetailedMarkdownFormatter(LoggerMixin):
    """Formats detailed code analysis into focused markdown reports"""
    
    def __init__(self):
        self.code_analyzer = DetailedCodeAnalyzer()
    
    def format_detailed_response(self, query_result: Dict, orchestration_result: any) -> str:
        """Format response with detailed code analysis and differences"""
        
        self.logger.info("Formatting detailed code analysis response")
        
        markdown_parts = []
        
        # Header with clear context for county employees
        use_case_display = query_result['use_case'].replace('_', ' ').title()
        markdown_parts.append(f"# � {use_case_display} Implementation Comparison")
        markdown_parts.append(f"**Analysis Request:** {query_result['original_query']}")
        markdown_parts.append("")

        # Add executive summary for county employees
        markdown_parts.append("## 📝 Executive Summary")
        if query_result['is_comparison']:
            markdown_parts.append("This report compares how different counties implement fee calculation functionality in their Accela systems. The analysis focuses on actual code differences and practical implementation approaches.")
        else:
            markdown_parts.append("This report analyzes the fee calculation implementation for the specified county, showing actual code examples and implementation details.")
        markdown_parts.append("")
        
        # Get best implementation for detailed analysis
        if orchestration_result.best_implementation:
            best_county = orchestration_result.best_implementation.get('county', 'Unknown')
            
            try:
                # Get detailed implementation analysis
                detailed_impl = self.code_analyzer.get_detailed_implementation(
                    best_county, query_result['use_case']
                )
                
                if 'error' not in detailed_impl:
                    markdown_parts.append(self._format_implementation_details(detailed_impl))
                
                # If comparison query, compare with alternatives
                if query_result['is_comparison'] and orchestration_result.alternatives:
                    alt_county = orchestration_result.alternatives[0].get('county', 'Unknown')
                    if alt_county != best_county:
                        comparison = self.code_analyzer.compare_implementations_detailed(
                            best_county, alt_county, query_result['use_case']
                        )
                        
                        if 'error' not in comparison:
                            markdown_parts.append(self._format_detailed_comparison(comparison))
                
            except Exception as e:
                self.logger.error(f"Detailed analysis failed: {e}")
                markdown_parts.append("*Detailed code analysis temporarily unavailable*")
        
        return "\n".join(markdown_parts)
    
    def _format_implementation_details(self, implementation: Dict) -> str:
        """Format detailed implementation analysis"""
        
        parts = []
        county = implementation['county'].replace('_', ' ').title()
        
        parts.append(f"## �️ {county} Implementation Details")
        parts.append("")

        # Practical implementation summary
        parts.append("### � Implementation Summary")
        parts.append(f"**County:** {county}")
        parts.append(f"**Scripts Analyzed:** {implementation['total_scripts']} relevant files found")
        parts.append(f"**Functions Identified:** {len(implementation['key_functions'])} fee-related functions")

        # Show practical insights instead of technical scores
        if implementation.get('actual_code_blocks'):
            code_types = set(block.get('type', 'Unknown') for block in implementation['actual_code_blocks'])
            parts.append(f"**Implementation Types:** {', '.join(code_types)}")

        parts.append("")

        # Only show issues if there are meaningful ones
        meaningful_issues = [issue for issue in implementation.get('issues', []) if not issue.startswith('⚠️ Limited')]
        if meaningful_issues:
            parts.append("### ⚠️ Code Analysis Notes")
            for issue in meaningful_issues:
                parts.append(f"- {issue}")
            parts.append("")
        
        # Key functions with actual code
        functions_with_code = [func for func in implementation['key_functions'] if func.get('code', '').strip()]

        if functions_with_code:
            parts.append("### 🔧 Key Functions Implementation")

            for func in functions_with_code[:3]:  # Show top 3 functions with actual code
                parts.append(f"#### `{func['name']}()` - {func['complexity']} Complexity")
                parts.append(f"**Lines of Code:** {func['lines']}")

                if func.get('best_practices'):
                    parts.append("**Best Practices Found:**")
                    for practice in func['best_practices']:
                        parts.append(f"- ✅ {practice}")

                # Show actual code only if it exists and is meaningful
                code = func['code'].strip()
                if code and len(code) > 10:  # Only show if there's substantial code
                    parts.append("**Code Implementation:**")
                    parts.append("```javascript")
                    parts.append(code[:800] + "..." if len(code) > 800 else code)
                    parts.append("```")
                parts.append("")
        elif implementation['key_functions']:
            # Show function names even if no code is available
            parts.append("### 🔧 Key Functions Found")
            for func in implementation['key_functions'][:5]:
                parts.append(f"- `{func['name']}()`")
            parts.append("")
        
        # Key code blocks - only show if they have meaningful content
        code_blocks_with_content = [block for block in implementation['actual_code_blocks']
                                   if block.get('code', '').strip() and len(block.get('code', '').strip()) > 20]

        if code_blocks_with_content:
            parts.append("### 🎯 Critical Code Blocks")

            high_importance = [block for block in code_blocks_with_content
                             if block.get('importance') == 'High']

            # Show high importance blocks first, then others
            blocks_to_show = high_importance[:2] + [block for block in code_blocks_with_content
                                                   if block not in high_importance][:1]

            for block in blocks_to_show:
                parts.append(f"#### {block['type']}")
                parts.append("```javascript")
                parts.append(block['code'])
                parts.append("```")
                parts.append("")
        
        # Practical recommendations for county staff
        practical_recommendations = [rec for rec in implementation.get('recommendations', [])
                                   if not rec.startswith('🔧') and not rec.startswith('📝')]

        if practical_recommendations:
            parts.append("### 💡 Implementation Notes")
            for rec in practical_recommendations:
                parts.append(f"- {rec}")
            parts.append("")
        
        return "\n".join(parts)
    
    def _format_detailed_comparison(self, comparison: Dict) -> str:
        """Format detailed comparison between two implementations"""
        
        parts = []
        county1 = comparison['county1'].replace('_', ' ').title()
        county2 = comparison['county2'].replace('_', ' ').title()
        
        parts.append(f"## 🔄 {county1} vs {county2} Implementation Comparison")
        parts.append("")
        
        # Summary metrics - only show meaningful comparisons
        summary = comparison['summary']
        parts.append("### 📈 Implementation Comparison")
        parts.append(f"| County | Functions Found | Scripts Analyzed |")
        parts.append("|--------|-----------------|------------------|")
        parts.append(f"| **{county1}** | {summary['county1_functions']} | - |")
        parts.append(f"| **{county2}** | {summary['county2_functions']} | - |")

        # Only show quality scores if they're meaningful
        if summary['county1_best_practices'] > 0 or summary['county2_best_practices'] > 0:
            parts.append("")
            parts.append(f"**Code Quality Notes:**")
            if summary['county1_best_practices'] > 0:
                parts.append(f"- {county1}: {summary['county1_best_practices']:.1f}% quality score")
            if summary['county2_best_practices'] > 0:
                parts.append(f"- {county2}: {summary['county2_best_practices']:.1f}% quality score")

        parts.append("")
        
        # Code differences
        code_diff = comparison['code_differences']
        if code_diff['unique_to_county1'] or code_diff['unique_to_county2']:
            parts.append("### 🔍 Code Pattern Differences")
            
            if code_diff['unique_to_county1']:
                parts.append(f"#### {county1} Unique Patterns")
                parts.append("```javascript")
                for pattern in code_diff['unique_to_county1'][:5]:
                    parts.append(pattern.strip())
                parts.append("```")
                parts.append("")
            
            if code_diff['unique_to_county2']:
                parts.append(f"#### {county2} Unique Patterns")
                parts.append("```javascript")
                for pattern in code_diff['unique_to_county2'][:5]:
                    parts.append(pattern.strip())
                parts.append("```")
                parts.append("")
        
        # Syntax differences
        if code_diff['syntax_differences']:
            parts.append("### 📝 Syntax Differences")
            parts.append("```diff")
            for diff_line in code_diff['syntax_differences'][:15]:  # Show first 15 diff lines
                parts.append(diff_line.rstrip())
            parts.append("```")
            parts.append("")
        
        # Best practices comparison
        bp_comparison = comparison['best_practices_comparison']
        if bp_comparison['gap_analysis']:
            parts.append("### 🏅 Best Practices Gap Analysis")
            
            winner = bp_comparison['winner'].replace('_', ' ').title()
            parts.append(f"**Overall Winner:** {winner}")
            parts.append("")
            
            for gap in bp_comparison['gap_analysis']:
                advantage = gap['advantage'].replace('_', ' ').title()
                parts.append(f"- **{gap['practice']}** - Advantage: {advantage} (Weight: {gap['weight']})")
            parts.append("")
        
        # Function comparison
        func_comparison = comparison['function_comparison']
        if func_comparison['complexity_comparison']:
            parts.append("### ⚙️ Function Complexity Comparison")
            parts.append("| Function | County | Complexity | Lines |")
            parts.append("|----------|--------|------------|-------|")
            
            for func_comp in func_comparison['complexity_comparison']:
                parts.append(f"| `{func_comp['function']}` | {county1} | {func_comp['county1_complexity']} | {func_comp['county1_lines']} |")
                parts.append(f"| `{func_comp['function']}` | {county2} | {func_comp['county2_complexity']} | {func_comp['county2_lines']} |")
            parts.append("")
        
        # Unique functions
        if func_comparison['unique_to_county1']:
            parts.append(f"#### {county1} Unique Functions")
            for func in func_comparison['unique_to_county1'][:5]:
                parts.append(f"- `{func}()`")
            parts.append("")
        
        if func_comparison['unique_to_county2']:
            parts.append(f"#### {county2} Unique Functions")
            for func in func_comparison['unique_to_county2'][:5]:
                parts.append(f"- `{func}()`")
            parts.append("")
        
        # Recommendations
        if comparison['recommendations']:
            parts.append("### 🎯 Implementation Recommendations")
            for rec in comparison['recommendations']:
                parts.append(f"- {rec}")
            parts.append("")
        
        # Accela best practices summary
        parts.append("### 📚 Accela Best Practices Summary")
        parts.append("Based on this analysis, here are the key Accela development practices:")
        parts.append("- ✅ **Error Handling:** Always use try-catch blocks for API calls")
        parts.append("- ✅ **Logging:** Implement comprehensive logging with logDebug/logMessage")
        parts.append("- ✅ **Null Checks:** Validate all inputs and API responses")
        parts.append("- ✅ **API Usage:** Use appropriate aa.* API methods")
        parts.append("- ✅ **Code Structure:** Keep functions focused and well-documented")
        parts.append("")
        
        return "\n".join(parts)
