"""
Dynamic Orchestrator for Accela Knowledge Base
Coordinates dynamic query processing, analysis, and response generation
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

from .logging import LoggerMixin
from .config import Config
from .dynamic_query_processor import DynamicQueryProcessor, DynamicQueryAnalysis
from .dynamic_analyzer import DynamicCountyAnalyzer, CountyAnalysisResult, ComparisonResult
from .dynamic_response_generator import DynamicResponseGenerator


class DynamicOrchestrationResult:
    """Result from dynamic orchestration"""
    
    def __init__(self, 
                 request_id: str,
                 original_query: str,
                 query_analysis: DynamicQueryAnalysis,
                 county_results: List[CountyAnalysisResult],
                 comparison_result: Optional[ComparisonResult],
                 markdown_response: str,
                 processing_time: float):
        self.request_id = request_id
        self.original_query = original_query
        self.query_analysis = query_analysis
        self.county_results = county_results
        self.comparison_result = comparison_result
        self.markdown_response = markdown_response
        self.processing_time = processing_time
        
        # Legacy compatibility properties
        self.best_implementation = self._get_best_implementation()
        self.alternatives = self._get_alternatives()
        self.synthesis = self._get_synthesis()
        self.confidence = query_analysis.confidence
        self.reasoning = self._get_reasoning()
        self.recommendations = self._get_recommendations()
    
    def _get_best_implementation(self) -> Dict[str, Any]:
        """Get best implementation for legacy compatibility"""
        if not self.county_results:
            return {}
        
        # Find highest confidence result
        best_result = max(self.county_results, key=lambda x: x.confidence)
        
        return {
            'county': best_result.county,
            'score': best_result.confidence,
            'metadata': best_result.metadata,
            'reasoning': f"Highest confidence implementation ({best_result.confidence:.2f})"
        }
    
    def _get_alternatives(self) -> List[Dict[str, Any]]:
        """Get alternatives for legacy compatibility"""
        alternatives = []
        
        # Sort by confidence and skip the best one
        sorted_results = sorted(self.county_results, key=lambda x: x.confidence, reverse=True)
        
        for result in sorted_results[1:4]:  # Top 3 alternatives
            alternatives.append({
                'county': result.county,
                'score': result.confidence,
                'metadata': result.metadata,
                'reasoning': f"Alternative implementation ({result.confidence:.2f})"
            })
        
        return alternatives
    
    def _get_synthesis(self) -> Dict[str, Any]:
        """Get synthesis for legacy compatibility"""
        if not self.county_results:
            return {}
        
        return {
            'optimal_approach': {
                'analysis_type': self.query_analysis.analysis_type,
                'counties_analyzed': len(self.county_results),
                'comparison_performed': self.comparison_result is not None,
                'key_findings': self.query_analysis.specific_aspects
            },
            'summary': f"Dynamic analysis of {len(self.county_results)} counties for {self.query_analysis.intent}"
        }
    
    def _get_reasoning(self) -> str:
        """Get reasoning for legacy compatibility"""
        reasoning_parts = [
            f"Intent: {self.query_analysis.intent}",
            f"Analysis: {self.query_analysis.analysis_type}",
            f"Counties: {len(self.county_results)}",
            f"Confidence: {self.query_analysis.confidence:.2f}"
        ]
        
        return " | ".join(reasoning_parts)
    
    def _get_recommendations(self) -> List[str]:
        """Get recommendations for legacy compatibility"""
        all_recommendations = []
        
        # Collect recommendations from county results
        for result in self.county_results:
            all_recommendations.extend(result.recommendations)
        
        # Add comparison recommendations if available
        if self.comparison_result:
            all_recommendations.extend(self.comparison_result.recommendations)
        
        # Remove duplicates and return top 5
        unique_recommendations = list(set(all_recommendations))
        return unique_recommendations[:5]


class DynamicOrchestrator(LoggerMixin):
    """
    Dynamic orchestrator that coordinates query processing, analysis, and response generation
    without hardcoded patterns or use cases
    """
    
    def __init__(self, config: Config):
        self.config = config
        
        # Initialize components
        self.query_processor = DynamicQueryProcessor(config)
        self.analyzer = DynamicCountyAnalyzer(config)
        self.response_generator = DynamicResponseGenerator(config)
        
        self.logger.info("Dynamic orchestrator initialized")
    
    async def orchestrate(self, query: str, counties: Optional[List[str]] = None) -> DynamicOrchestrationResult:
        """
        Orchestrate dynamic processing of any query about county data
        
        Args:
            query: Natural language query about county implementations
            counties: Optional list of specific counties to analyze
            
        Returns:
            DynamicOrchestrationResult with comprehensive analysis and response
        """
        
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        self.logger.info(f"Starting dynamic orchestration for: {query}")
        
        try:
            # Step 1: Process query to understand intent and extract information
            query_analysis = self.query_processor.process_query(query)
            
            # Override counties if specified
            if counties:
                query_analysis.entities['counties'] = counties
            
            self.logger.info(f"Query analysis complete. Intent: {query_analysis.intent}")
            
            # Step 2: Analyze relevant counties
            county_results = self.analyzer.analyze_counties(query_analysis)
            
            self.logger.info(f"County analysis complete. Analyzed {len(county_results)} counties")
            
            # Step 3: Perform comparison if multiple counties and requested
            comparison_result = None
            if len(county_results) > 1 and (query_analysis.comparison_requested or len(county_results) > 1):
                comparison_result = self.analyzer.compare_counties(query_analysis, county_results)
                self.logger.info("Comparison analysis complete")
            
            # Step 4: Generate comprehensive markdown response
            markdown_response = self.response_generator.generate_response(
                query_analysis, county_results, comparison_result
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create result
            result = DynamicOrchestrationResult(
                request_id=request_id,
                original_query=query,
                query_analysis=query_analysis,
                county_results=county_results,
                comparison_result=comparison_result,
                markdown_response=markdown_response,
                processing_time=processing_time
            )
            
            self.logger.info(f"Dynamic orchestration complete. Processing time: {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Dynamic orchestration failed: {e}")
            raise
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of the dynamic orchestration system"""
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "query_processor": "ready",
                "analyzer": "ready",
                "response_generator": "ready"
            },
            "llm_enabled": self.config.llm_enabled,
            "capabilities": [
                "dynamic_query_understanding",
                "flexible_county_analysis", 
                "intelligent_comparison",
                "contextual_response_generation"
            ]
        }
    
    # Legacy compatibility methods
    async def orchestrate_legacy(self, request) -> DynamicOrchestrationResult:
        """Legacy compatibility method for existing OrchestrationRequest"""
        
        # Extract query and counties from legacy request
        query = request.query
        counties = request.target_counties
        
        # Use dynamic orchestration
        return await self.orchestrate(query, counties)
