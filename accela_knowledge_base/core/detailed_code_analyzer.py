"""
Detailed code analyzer for showing actual code differences and Accela best practices
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from .logging import LoggerMixin
import difflib


class DetailedCodeAnalyzer(LoggerMixin):
    """Analyzes actual code implementations and shows detailed differences"""
    
    def __init__(self, metadata_file: str = "accela_metadata.json"):
        self.metadata_file = metadata_file
        self.metadata = self._load_metadata()
        
        # Accela best practices patterns
        self.best_practices = {
            'error_handling': {
                'pattern': r'try\s*{.*?catch\s*\([^)]*\)',
                'description': 'Proper error handling with try-catch blocks',
                'weight': 10
            },
            'logging': {
                'pattern': r'(logDebug|logMessage|aa\.print)',
                'description': 'Proper logging for debugging and monitoring',
                'weight': 8
            },
            'null_checks': {
                'pattern': r'(!=\s*null|!==\s*null|exists\()',
                'description': 'Null/undefined checks before operations',
                'weight': 9
            },
            'email_validation': {
                'pattern': r'(validateEmail|@.*\.|\.com|\.org)',
                'description': 'Email address validation',
                'weight': 7
            },
            'fee_calculation': {
                'pattern': r'(addFee|updateFee|feeAmount|invoiceFee)',
                'description': 'Proper fee calculation methods',
                'weight': 9
            },
            'workflow_management': {
                'pattern': r'(updateTask|assignTask|closeTask|activateTask)',
                'description': 'Workflow task management',
                'weight': 8
            },
            'data_validation': {
                'pattern': r'(isEmpty|isBlank|matches|validateGisObjects)',
                'description': 'Input data validation',
                'weight': 9
            },
            'api_usage': {
                'pattern': r'aa\.[a-zA-Z]+\.[a-zA-Z]+',
                'description': 'Proper Accela API usage',
                'weight': 10
            }
        }
    
    def _load_metadata(self) -> Dict:
        """Load metadata from file"""
        try:
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load metadata: {e}")
            return {}
    
    def get_detailed_implementation(self, county: str, use_case: str) -> Dict[str, any]:
        """Get detailed implementation with actual code snippets"""

        # Filter scripts by county
        county_scripts = [script for script in self.metadata if script.get('county') == county]
        if not county_scripts:
            return {'error': f'County {county} not found'}

        # Find relevant scripts
        relevant_scripts = self._find_use_case_scripts(county_scripts, use_case)
        
        implementation = {
            'county': county,
            'use_case': use_case,
            'total_scripts': len(relevant_scripts),
            'code_snippets': [],
            'best_practices_score': 0,
            'issues': [],
            'recommendations': [],
            'key_functions': [],
            'actual_code_blocks': []
        }
        
        # Analyze each relevant script
        for script_data in relevant_scripts:
            script_analysis = self._analyze_script_detailed(script_data, use_case)
            implementation['code_snippets'].extend(script_analysis['snippets'])
            implementation['key_functions'].extend(script_analysis['functions'])
            implementation['actual_code_blocks'].extend(script_analysis['code_blocks'])
        
        # Calculate best practices score based on actual code content
        all_code = []
        for script_data in relevant_scripts:
            file_content = self._read_file_content(script_data.get('file_path', ''))
            if file_content:
                all_code.append(file_content)

        implementation['best_practices_score'] = self._calculate_best_practices_score(all_code)
        
        # Identify issues and recommendations based on actual code
        implementation['issues'] = self._identify_code_issues(all_code)
        implementation['recommendations'] = self._generate_code_recommendations(implementation)
        
        return implementation
    
    def _find_use_case_scripts(self, county_scripts: List, use_case: str) -> List:
        """Find scripts relevant to specific use case"""
        
        use_case_keywords = {
            'notification': ['email', 'sendmail', 'notify', 'alert', 'message', 'notification'],
            'fee_calculation': ['fee', 'invoice', 'payment', 'calculate', 'amount', 'cost'],
            'permit_processing': ['permit', 'application', 'cap', 'record', 'license'],
            'inspection': ['inspection', 'inspect', 'schedule', 'result'],
            'workflow_automation': ['workflow', 'task', 'assign', 'route', 'approve']
        }
        
        keywords = use_case_keywords.get(use_case, [use_case])
        relevant_scripts = []

        for script_data in county_scripts:
            content = script_data.get('content', '').lower()
            functions = script_data.get('functions', [])

            # Check if script is relevant
            relevance_score = 0
            for keyword in keywords:
                if keyword in content:
                    relevance_score += content.count(keyword)

                # Check function names
                for func_name in functions:
                    if isinstance(func_name, str) and keyword in func_name.lower():
                        relevance_score += 2

            if relevance_score > 0:
                script_data['relevance_score'] = relevance_score
                relevant_scripts.append(script_data)

        # Sort by relevance and return top 5
        sorted_scripts = sorted(relevant_scripts,
                               key=lambda x: x.get('relevance_score', 0),
                               reverse=True)[:5]

        return sorted_scripts
    
    def _analyze_script_detailed(self, script_data: Dict, use_case: str) -> Dict:
        """Analyze script in detail for actual code"""

        # Read actual file content instead of relying on metadata
        file_path = script_data.get('file_path', '')
        content = self._read_file_content(file_path)
        functions = script_data.get('functions', [])

        # Handle case where functions might be a list of strings instead of objects
        if functions and isinstance(functions[0], str):
            # Convert function names to function objects and extract their code
            functions = [{'name': func_name, 'code': self._extract_function_code(content, func_name)} for func_name in functions]

        analysis = {
            'snippets': [],
            'functions': [],
            'code_blocks': []
        }

        # Extract relevant functions with actual code
        for func in functions:
            if isinstance(func, dict):
                func_name = func.get('name', '')
                func_code = func.get('code', '')
            else:
                func_name = str(func)
                func_code = self._extract_function_code(content, func_name)

            if self._is_function_relevant(func_name, func_code or content, use_case):
                # Use extracted function code or fallback to content
                code_to_analyze = func_code if func_code else content

                analysis['functions'].append({
                    'name': func_name,
                    'code': code_to_analyze[:800] + '...' if len(code_to_analyze) > 800 else code_to_analyze,
                    'lines': len(code_to_analyze.split('\n')) if code_to_analyze else 0,
                    'complexity': self._calculate_function_complexity(code_to_analyze) if code_to_analyze else 'Medium',
                    'best_practices': self._check_function_best_practices(code_to_analyze) if code_to_analyze else []
                })

                # Extract key code blocks from content
                if code_to_analyze:
                    key_blocks = self._extract_key_code_blocks(code_to_analyze, use_case)
                    analysis['code_blocks'].extend(key_blocks)

        return analysis
    
    def _is_function_relevant(self, func_name: str, func_code: str, use_case: str) -> bool:
        """Check if function is relevant to use case"""
        
        use_case_patterns = {
            'notification': ['email', 'send', 'notify', 'alert', 'message'],
            'fee_calculation': ['fee', 'invoice', 'payment', 'calculate', 'amount'],
            'permit_processing': ['permit', 'application', 'cap', 'record'],
            'inspection': ['inspection', 'inspect', 'schedule'],
            'workflow_automation': ['workflow', 'task', 'assign', 'route']
        }
        
        patterns = use_case_patterns.get(use_case, [use_case])
        
        for pattern in patterns:
            if pattern in func_name.lower() or pattern in func_code.lower():
                return True
        
        return False

    def _read_file_content(self, file_path: str) -> str:
        """Read actual file content from disk"""

        if not file_path:
            return ''

        try:
            # Handle relative paths from repository root
            if not file_path.startswith('/'):
                file_path = Path.cwd() / file_path
            else:
                file_path = Path(file_path)

            if file_path.exists() and file_path.is_file():
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
        except Exception as e:
            self.logger.warning(f"Failed to read file {file_path}: {e}")

        return ''

    def _extract_function_code(self, content: str, func_name: str) -> str:
        """Extract specific function code from file content"""

        if not content or not func_name:
            return ''

        # Pattern to match function definition
        patterns = [
            rf'function\s+{re.escape(func_name)}\s*\([^)]*\)\s*{{[^}}]*}}',  # Simple function
            rf'function\s+{re.escape(func_name)}\s*\([^)]*\)\s*{{.*?^}}',    # Multi-line function
            rf'{re.escape(func_name)}\s*=\s*function[^{{]*{{[^}}]*}}',       # Function assignment
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            if matches:
                # Return the first (usually best) match
                return matches[0]

        # If no exact function match, look for function calls or references
        lines = content.split('\n')
        func_lines = []
        in_function = False
        brace_count = 0

        for line in lines:
            if f'function {func_name}' in line or f'{func_name} = function' in line:
                in_function = True
                brace_count = 0
                func_lines.append(line)
            elif in_function:
                func_lines.append(line)
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0 and '}' in line:
                    break

        return '\n'.join(func_lines) if func_lines else ''

    def _clean_code(self, code: str) -> str:
        """Clean and format code for display"""
        
        lines = code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Remove excessive whitespace but preserve indentation
            cleaned_line = re.sub(r'[ \t]+', ' ', line.rstrip())
            if cleaned_line.strip():  # Skip empty lines
                cleaned_lines.append(cleaned_line)
        
        return '\n'.join(cleaned_lines)
    
    def _calculate_function_complexity(self, code: str) -> str:
        """Calculate function complexity"""
        
        complexity_indicators = {
            'if': code.count('if '),
            'for': code.count('for '),
            'while': code.count('while '),
            'try': code.count('try '),
            'nested_calls': len(re.findall(r'aa\.[a-zA-Z]+\.[a-zA-Z]+', code))
        }
        
        total_complexity = sum(complexity_indicators.values())
        
        if total_complexity < 5:
            return 'Low'
        elif total_complexity < 15:
            return 'Medium'
        else:
            return 'High'
    
    def _check_function_best_practices(self, code: str) -> List[str]:
        """Check function against Accela best practices"""
        
        practices_found = []
        
        for practice_name, practice_info in self.best_practices.items():
            if re.search(practice_info['pattern'], code, re.IGNORECASE | re.DOTALL):
                practices_found.append(practice_info['description'])
        
        return practices_found
    
    def _extract_key_code_blocks(self, code: str, use_case: str) -> List[Dict]:
        """Extract key code blocks relevant to use case"""
        
        blocks = []
        
        # Email sending blocks
        if use_case == 'notification':
            email_blocks = re.findall(r'(sendMail\([^;]*;)', code, re.IGNORECASE)
            for block in email_blocks:
                blocks.append({
                    'type': 'Email Sending',
                    'code': block,
                    'importance': 'High'
                })
        
        # Fee calculation blocks
        elif use_case == 'fee_calculation':
            # Extract more comprehensive fee calculation patterns
            fee_patterns = [
                r'(function\s+(?:addFee|updateFee|feeAmount|invoiceFee|calculateFee)[^{]*{[^}]*})',
                r'((?:addFee|updateFee|feeAmount|invoiceFee)\([^)]*\)[^;]*;)',
                r'(aa\.fee\.[^;]*;)',
                r'(aa\.finance\.[^;]*;)',
                r'(feeTotal\s*[+\-*/=][^;]*;)',
                r'(getFeeItems[^;]*;)',
                r'(createFeeItem[^;]*;)'
            ]

            for pattern in fee_patterns:
                matches = re.findall(pattern, code, re.IGNORECASE | re.DOTALL)
                for match in matches[:3]:  # Limit to 3 per pattern
                    blocks.append({
                        'type': 'Fee Calculation',
                        'code': match[:300] + '...' if len(match) > 300 else match,
                        'importance': 'High'
                    })
        
        # API calls
        api_calls = re.findall(r'(aa\.[a-zA-Z]+\.[a-zA-Z]+[^;]*;)', code)
        for call in api_calls[:5]:  # Limit to top 5
            blocks.append({
                'type': 'Accela API Call',
                'code': call,
                'importance': 'Medium'
            })
        
        # Error handling blocks
        error_blocks = re.findall(r'(try\s*{[^}]*}[^}]*catch[^}]*})', code, re.DOTALL)
        for block in error_blocks:
            blocks.append({
                'type': 'Error Handling',
                'code': block[:200] + '...' if len(block) > 200 else block,
                'importance': 'High'
            })
        
        return blocks
    
    def _calculate_best_practices_score(self, code_snippets: List) -> float:
        """Calculate overall best practices score"""
        
        total_score = 0
        max_score = 0
        
        for practice_name, practice_info in self.best_practices.items():
            max_score += practice_info['weight']
            
            # Check if practice is found in any code snippet
            found = False
            for snippet in code_snippets:
                if re.search(practice_info['pattern'], str(snippet), re.IGNORECASE):
                    found = True
                    break
            
            if found:
                total_score += practice_info['weight']
        
        return (total_score / max_score) * 100 if max_score > 0 else 0
    
    def _identify_code_issues(self, code_snippets: List) -> List[str]:
        """Identify potential issues in code"""
        
        issues = []
        code_text = ' '.join(str(snippet) for snippet in code_snippets)
        
        # Check for common issues
        if 'try' not in code_text.lower():
            issues.append("❌ Missing error handling - No try-catch blocks found")
        
        if not re.search(r'(logDebug|logMessage)', code_text):
            issues.append("⚠️ Limited logging - Consider adding debug logging")
        
        if not re.search(r'(!=\s*null|!==\s*null)', code_text):
            issues.append("⚠️ Missing null checks - Add validation for null values")
        
        if code_text.count('aa.') < 3:
            issues.append("⚠️ Limited Accela API usage - Consider using more API functions")
        
        # Check for hardcoded values
        if re.search(r'["\'][^"\']*@[^"\']*["\']', code_text):
            issues.append("⚠️ Hardcoded email addresses found - Use configuration instead")
        
        return issues
    
    def _generate_code_recommendations(self, implementation: Dict) -> List[str]:
        """Generate specific code recommendations"""
        
        recommendations = []
        score = implementation['best_practices_score']
        
        if score < 50:
            recommendations.append("🔧 Improve error handling with try-catch blocks")
            recommendations.append("📝 Add comprehensive logging for debugging")
        elif score < 75:
            recommendations.append("✨ Enhance input validation and null checks")
            recommendations.append("🚀 Optimize Accela API usage patterns")
        else:
            recommendations.append("✅ Code follows most Accela best practices")
            recommendations.append("🎯 Consider performance optimizations")
        
        # Specific recommendations based on use case
        use_case = implementation['use_case']
        if use_case == 'notification':
            recommendations.append("📧 Implement email template validation")
            recommendations.append("🔄 Add retry logic for failed email sends")
        elif use_case == 'fee_calculation':
            recommendations.append("💰 Validate fee amounts before processing")
            recommendations.append("📊 Add audit logging for fee changes")
        
        return recommendations

    def compare_implementations_detailed(self, county1: str, county2: str, use_case: str) -> Dict[str, any]:
        """Compare two implementations with detailed code analysis"""

        impl1 = self.get_detailed_implementation(county1, use_case)
        impl2 = self.get_detailed_implementation(county2, use_case)

        if 'error' in impl1 or 'error' in impl2:
            return {'error': 'One or both counties not found'}

        comparison = {
            'county1': county1,
            'county2': county2,
            'use_case': use_case,
            'summary': {
                'county1_functions': len(impl1['key_functions']),
                'county2_functions': len(impl2['key_functions']),
                'county1_best_practices': impl1['best_practices_score'],
                'county2_best_practices': impl2['best_practices_score'],
                'difference_percentage': abs(impl1['best_practices_score'] - impl2['best_practices_score'])
            },
            'code_differences': self._analyze_code_differences(impl1, impl2),
            'best_practices_comparison': self._compare_best_practices(impl1, impl2),
            'function_comparison': self._compare_functions(impl1['key_functions'], impl2['key_functions']),
            'recommendations': self._generate_comparison_recommendations(impl1, impl2)
        }

        return comparison

    def _analyze_code_differences(self, impl1: Dict, impl2: Dict) -> Dict:
        """Analyze actual code differences between implementations"""

        differences = {
            'unique_to_county1': [],
            'unique_to_county2': [],
            'common_patterns': [],
            'syntax_differences': []
        }

        # Get all code blocks from both implementations
        code1_blocks = [block['code'] for block in impl1.get('actual_code_blocks', [])]
        code2_blocks = [block['code'] for block in impl2.get('actual_code_blocks', [])]

        # Find unique code patterns
        code1_text = '\n'.join(code1_blocks)
        code2_text = '\n'.join(code2_blocks)

        # Extract function calls
        calls1 = set(re.findall(r'[a-zA-Z_][a-zA-Z0-9_]*\s*\(', code1_text))
        calls2 = set(re.findall(r'[a-zA-Z_][a-zA-Z0-9_]*\s*\(', code2_text))

        differences['unique_to_county1'] = list(calls1 - calls2)
        differences['unique_to_county2'] = list(calls2 - calls1)
        differences['common_patterns'] = list(calls1 & calls2)

        # Find syntax differences using difflib
        diff = list(difflib.unified_diff(
            code1_text.splitlines(keepends=True),
            code2_text.splitlines(keepends=True),
            fromfile=f'{impl1["county"]}.js',
            tofile=f'{impl2["county"]}.js',
            n=3
        ))

        differences['syntax_differences'] = diff[:20]  # Limit to first 20 differences

        return differences

    def _compare_best_practices(self, impl1: Dict, impl2: Dict) -> Dict:
        """Compare best practices between implementations"""

        comparison = {
            'county1_score': impl1['best_practices_score'],
            'county2_score': impl2['best_practices_score'],
            'winner': impl1['county'] if impl1['best_practices_score'] > impl2['best_practices_score'] else impl2['county'],
            'gap_analysis': []
        }

        # Analyze specific practice gaps
        for practice_name, practice_info in self.best_practices.items():
            impl1_has = any(practice_info['description'] in str(func.get('best_practices', []))
                           for func in impl1.get('key_functions', []))
            impl2_has = any(practice_info['description'] in str(func.get('best_practices', []))
                           for func in impl2.get('key_functions', []))

            if impl1_has and not impl2_has:
                comparison['gap_analysis'].append({
                    'practice': practice_info['description'],
                    'advantage': impl1['county'],
                    'weight': practice_info['weight']
                })
            elif impl2_has and not impl1_has:
                comparison['gap_analysis'].append({
                    'practice': practice_info['description'],
                    'advantage': impl2['county'],
                    'weight': practice_info['weight']
                })

        return comparison

    def _compare_functions(self, functions1: List, functions2: List) -> Dict:
        """Compare functions between implementations"""

        func_names1 = {f['name'] for f in functions1}
        func_names2 = {f['name'] for f in functions2}

        comparison = {
            'total_functions_county1': len(functions1),
            'total_functions_county2': len(functions2),
            'common_functions': list(func_names1 & func_names2),
            'unique_to_county1': list(func_names1 - func_names2),
            'unique_to_county2': list(func_names2 - func_names1),
            'complexity_comparison': []
        }

        # Compare complexity of common functions
        for func_name in comparison['common_functions'][:5]:  # Top 5 common functions
            func1 = next((f for f in functions1 if f['name'] == func_name), None)
            func2 = next((f for f in functions2 if f['name'] == func_name), None)

            if func1 and func2:
                comparison['complexity_comparison'].append({
                    'function': func_name,
                    'county1_complexity': func1.get('complexity', 'Unknown'),
                    'county2_complexity': func2.get('complexity', 'Unknown'),
                    'county1_lines': func1.get('lines', 0),
                    'county2_lines': func2.get('lines', 0)
                })

        return comparison

    def _generate_comparison_recommendations(self, impl1: Dict, impl2: Dict) -> List[str]:
        """Generate recommendations based on comparison"""

        recommendations = []

        score1 = impl1['best_practices_score']
        score2 = impl2['best_practices_score']

        if score1 > score2:
            recommendations.append(f"✅ {impl1['county']} has better code quality ({score1:.1f}% vs {score2:.1f}%)")
            recommendations.append(f"📋 Consider adopting {impl1['county']}'s practices in {impl2['county']}")
        elif score2 > score1:
            recommendations.append(f"✅ {impl2['county']} has better code quality ({score2:.1f}% vs {score1:.1f}%)")
            recommendations.append(f"📋 Consider adopting {impl2['county']}'s practices in {impl1['county']}")
        else:
            recommendations.append("⚖️ Both implementations have similar code quality")

        # Function-specific recommendations
        func_count1 = len(impl1.get('key_functions', []))
        func_count2 = len(impl2.get('key_functions', []))

        if func_count1 > func_count2:
            recommendations.append(f"🔧 {impl1['county']} has more comprehensive implementation ({func_count1} vs {func_count2} functions)")
        elif func_count2 > func_count1:
            recommendations.append(f"🔧 {impl2['county']} has more comprehensive implementation ({func_count2} vs {func_count1} functions)")

        # Issue-based recommendations
        issues1 = len(impl1.get('issues', []))
        issues2 = len(impl2.get('issues', []))

        if issues1 < issues2:
            recommendations.append(f"🐛 {impl1['county']} has fewer code issues ({issues1} vs {issues2})")
        elif issues2 < issues1:
            recommendations.append(f"🐛 {impl2['county']} has fewer code issues ({issues2} vs {issues1})")

        return recommendations
