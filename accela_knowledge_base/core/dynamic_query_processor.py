"""
Dynamic LLM-Powered Query Processor for Accela Knowledge Base
Handles any question about county data using LLM intelligence
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from .logging import LoggerMixin
from .config import Config
from ..llm.llm_helper import LLMHelper


@dataclass
class DynamicQueryAnalysis:
    """Result of dynamic query analysis"""
    original_query: str
    intent: str  # what the user wants to know
    entities: Dict[str, List[str]]  # counties, concepts, technical terms
    comparison_requested: bool
    analysis_type: str  # code_analysis, configuration_comparison, workflow_analysis, etc.
    specific_aspects: List[str]  # specific things to look for
    output_requirements: Dict[str, Any]  # how to format the response
    confidence: float


class DynamicQueryProcessor(LoggerMixin):
    """
    Dynamic query processor that uses LLM intelligence to understand
    any question about county data without hardcoded patterns
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.llm_helper = LLMHelper(config) if config.llm_enabled else None
        
        # Load available counties and basic metadata for context
        self.available_counties = self._load_available_counties()
        self.available_concepts = self._load_available_concepts()
        
        self.logger.info("Dynamic query processor initialized")
    
    def process_query(self, query: str) -> DynamicQueryAnalysis:
        """
        Process any natural language query about county data
        
        Args:
            query: Natural language question about county implementations
            
        Returns:
            DynamicQueryAnalysis with extracted information and analysis plan
        """
        
        self.logger.info(f"Processing dynamic query: {query}")
        
        if self.llm_helper:
            return self._process_with_llm(query)
        else:
            return self._process_with_fallback(query)
    
    def _process_with_llm(self, query: str) -> DynamicQueryAnalysis:
        """Process query using LLM intelligence"""
        
        try:
            # Build comprehensive prompt for query analysis
            prompt = self._build_query_analysis_prompt(query)
            
            response = self.llm_helper.client.chat.completions.create(
                model=self.config.llm_model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.1  # Low temperature for consistent analysis
            )
            
            # Parse LLM response
            llm_analysis = self._parse_llm_analysis(response.choices[0].message.content)
            
            # Create structured analysis
            analysis = DynamicQueryAnalysis(
                original_query=query,
                intent=llm_analysis.get('intent', 'general_inquiry'),
                entities=llm_analysis.get('entities', {}),
                comparison_requested=llm_analysis.get('comparison_requested', False),
                analysis_type=llm_analysis.get('analysis_type', 'general_analysis'),
                specific_aspects=llm_analysis.get('specific_aspects', []),
                output_requirements=llm_analysis.get('output_requirements', {}),
                confidence=llm_analysis.get('confidence', 0.8)
            )
            
            self.logger.info(f"LLM analysis complete. Intent: {analysis.intent}, Type: {analysis.analysis_type}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"LLM query processing failed: {e}")
            return self._process_with_fallback(query)
    
    def _build_query_analysis_prompt(self, query: str) -> str:
        """Build comprehensive prompt for LLM query analysis"""
        
        return f"""
You are an expert analyst for Accela government software implementations. Analyze this user query and extract structured information.

USER QUERY: "{query}"

AVAILABLE COUNTIES: {', '.join(self.available_counties)}

AVAILABLE CONCEPTS: {', '.join(self.available_concepts)}

Please analyze the query and respond with a JSON object containing:

1. "intent": What the user wants to know (e.g., "compare_implementations", "find_best_practice", "understand_workflow", "get_code_examples")

2. "entities": {{
   "counties": [list of county names mentioned or implied],
   "technical_concepts": [Accela concepts like "permits", "inspections", "workflows", "events", "fees"],
   "specific_terms": [specific technical terms, function names, or processes mentioned]
}}

3. "comparison_requested": true/false - whether user wants to compare between counties

4. "analysis_type": The type of analysis needed:
   - "code_analysis": Looking at actual code implementations
   - "workflow_comparison": Comparing business processes
   - "configuration_analysis": Looking at settings and configurations  
   - "best_practices": Finding optimal approaches
   - "troubleshooting": Solving problems or issues
   - "feature_exploration": Understanding capabilities
   - "general_inquiry": General questions

5. "specific_aspects": [List of specific things to look for, analyze, or compare]

6. "output_requirements": {{
   "format": "markdown",
   "include_code": true/false,
   "include_examples": true/false,
   "detail_level": "high/medium/low",
   "focus_areas": [specific areas to emphasize in response]
}}

7. "confidence": 0.0-1.0 confidence in the analysis

Respond ONLY with valid JSON. Be thorough in extracting entities and specific aspects.
"""
    
    def _parse_llm_analysis(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM response into structured data"""
        
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            else:
                self.logger.warning("No JSON found in LLM response")
                return {}
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse LLM JSON response: {e}")
            return {}
    
    def _process_with_fallback(self, query: str) -> DynamicQueryAnalysis:
        """Fallback processing when LLM is not available"""
        
        query_lower = query.lower()
        
        # Basic entity extraction
        entities = {
            'counties': self._extract_counties_fallback(query_lower),
            'technical_concepts': self._extract_concepts_fallback(query_lower),
            'specific_terms': self._extract_terms_fallback(query_lower)
        }
        
        # Basic intent detection
        intent = self._detect_intent_fallback(query_lower)
        
        # Basic analysis type detection
        analysis_type = self._detect_analysis_type_fallback(query_lower)
        
        # Check for comparison
        comparison_requested = any(word in query_lower for word in 
                                ['compare', 'difference', 'vs', 'versus', 'between'])
        
        return DynamicQueryAnalysis(
            original_query=query,
            intent=intent,
            entities=entities,
            comparison_requested=comparison_requested,
            analysis_type=analysis_type,
            specific_aspects=self._extract_aspects_fallback(query_lower),
            output_requirements={
                'format': 'markdown',
                'include_code': True,
                'include_examples': True,
                'detail_level': 'high'
            },
            confidence=0.6
        )
    
    def _load_available_counties(self) -> List[str]:
        """Load list of available counties from configuration"""
        
        # Basic county list - in production this would come from metadata
        return [
            'asheville', 'santa_barbara', 'marin', 'san_mateo', 'alameda',
            'contra_costa', 'sonoma', 'napa', 'solano', 'mendocino',
            'dayton', 'leon', 'atlanta_chattanooga', 'lancaster', 'cok'
        ]
    
    def _load_available_concepts(self) -> List[str]:
        """Load list of available Accela concepts"""
        
        return [
            'permits', 'inspections', 'licenses', 'planning', 'code_enforcement',
            'workflows', 'events', 'fees', 'notifications', 'reports',
            'applications', 'reviews', 'approvals', 'conditions', 'documents'
        ]
    
    def _extract_counties_fallback(self, query: str) -> List[str]:
        """Extract county names using fallback method"""
        
        found_counties = []
        for county in self.available_counties:
            if county.replace('_', ' ') in query or county in query:
                found_counties.append(county)
        
        return found_counties
    
    def _extract_concepts_fallback(self, query: str) -> List[str]:
        """Extract technical concepts using fallback method"""
        
        found_concepts = []
        for concept in self.available_concepts:
            if concept in query:
                found_concepts.append(concept)
        
        return found_concepts
    
    def _extract_terms_fallback(self, query: str) -> List[str]:
        """Extract specific technical terms using fallback method"""
        
        # Look for function-like terms, API calls, etc.
        terms = []
        
        # Function patterns
        func_pattern = r'\b\w+\(\)'
        terms.extend(re.findall(func_pattern, query))
        
        # API patterns
        api_pattern = r'\b[A-Z][a-zA-Z]*\.[a-zA-Z]+\b'
        terms.extend(re.findall(api_pattern, query))
        
        return terms
    
    def _detect_intent_fallback(self, query: str) -> str:
        """Detect user intent using fallback method"""
        
        if any(word in query for word in ['compare', 'difference', 'vs']):
            return 'compare_implementations'
        elif any(word in query for word in ['best', 'optimal', 'recommend']):
            return 'find_best_practice'
        elif any(word in query for word in ['how', 'workflow', 'process']):
            return 'understand_workflow'
        elif any(word in query for word in ['code', 'function', 'implementation']):
            return 'get_code_examples'
        else:
            return 'general_inquiry'
    
    def _detect_analysis_type_fallback(self, query: str) -> str:
        """Detect analysis type using fallback method"""
        
        if any(word in query for word in ['code', 'function', 'script']):
            return 'code_analysis'
        elif any(word in query for word in ['workflow', 'process', 'business']):
            return 'workflow_comparison'
        elif any(word in query for word in ['config', 'setting', 'setup']):
            return 'configuration_analysis'
        elif any(word in query for word in ['best', 'practice', 'optimal']):
            return 'best_practices'
        else:
            return 'general_inquiry'
    
    def _extract_aspects_fallback(self, query: str) -> List[str]:
        """Extract specific aspects to analyze using fallback method"""
        
        aspects = []
        
        # Common aspects based on query content
        if 'fee' in query:
            aspects.extend(['fee_calculation', 'fee_structure', 'payment_processing'])
        if 'email' in query:
            aspects.extend(['email_templates', 'notification_triggers', 'recipient_logic'])
        if 'workflow' in query:
            aspects.extend(['approval_steps', 'routing_logic', 'status_transitions'])
        if 'inspection' in query:
            aspects.extend(['scheduling', 'results_processing', 'follow_up_actions'])
        
        return aspects if aspects else ['general_implementation']
