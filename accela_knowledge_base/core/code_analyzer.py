"""
Enhanced code analyzer for extracting and highlighting code differences
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from .logging import LoggerMixin


class CodeAnalyzer(LoggerMixin):
    """Analyzes code implementations and extracts key differences"""
    
    def __init__(self, metadata_file: str = "accela_metadata.json"):
        self.metadata_file = metadata_file
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict:
        """Load metadata from file"""
        try:
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load metadata: {e}")
            return {}
    
    def get_actual_code_snippets(self, county: str, use_case: str) -> Dict[str, any]:
        """Get actual code snippets for a county's implementation"""
        
        county_data = self.metadata.get('counties', {}).get(county, {})
        if not county_data:
            return {}
        
        # Find relevant scripts based on use case
        relevant_scripts = self._find_relevant_scripts(county_data, use_case)
        
        code_analysis = {
            'county': county,
            'use_case': use_case,
            'key_functions': [],
            'event_handlers': [],
            'email_functions': [],
            'workflow_functions': [],
            'utility_functions': [],
            'code_snippets': [],
            'patterns': []
        }
        
        for script_path, script_data in relevant_scripts.items():
            self._analyze_script(script_data, code_analysis)
        
        return code_analysis
    
    def _find_relevant_scripts(self, county_data: Dict, use_case: str) -> Dict:
        """Find scripts relevant to the use case"""
        
        relevant_scripts = {}
        
        # Use case keywords mapping
        use_case_keywords = {
            'notification': ['email', 'sendmail', 'notify', 'alert', 'message'],
            'permit_processing': ['permit', 'application', 'cap', 'record'],
            'inspection': ['inspection', 'inspect', 'schedule'],
            'workflow_automation': ['workflow', 'wtua', 'task'],
            'fee_calculation': ['fee', 'invoice', 'payment', 'calculate'],
            'document_generation': ['document', 'report', 'pdf', 'generate'],
            'condition_management': ['condition', 'requirement'],
            'address_validation': ['address', 'parcel', 'gis'],
            'renewal_processing': ['renewal', 'expire', 'expiration'],
            'compliance_checking': ['compliance', 'zoning', 'code']
        }
        
        keywords = use_case_keywords.get(use_case, [use_case])
        
        for script_path, script_data in county_data.get('scripts', {}).items():
            script_content = script_data.get('content', '').lower()
            functions = script_data.get('functions', [])
            
            # Check if script is relevant
            is_relevant = False
            
            # Check content for keywords
            for keyword in keywords:
                if keyword in script_content:
                    is_relevant = True
                    break
            
            # Check function names
            if not is_relevant:
                for func in functions:
                    func_name = func.get('name', '').lower()
                    for keyword in keywords:
                        if keyword in func_name:
                            is_relevant = True
                            break
                    if is_relevant:
                        break
            
            if is_relevant:
                relevant_scripts[script_path] = script_data
        
        return relevant_scripts
    
    def _analyze_script(self, script_data: Dict, code_analysis: Dict):
        """Analyze a single script and extract key information"""
        
        functions = script_data.get('functions', [])
        content = script_data.get('content', '')
        
        for func in functions:
            func_name = func.get('name', '')
            func_code = func.get('code', '')
            
            # Categorize functions
            if self._is_email_function(func_name, func_code):
                code_analysis['email_functions'].append({
                    'name': func_name,
                    'code': func_code,
                    'key_features': self._extract_email_features(func_code)
                })
            
            elif self._is_workflow_function(func_name, func_code):
                code_analysis['workflow_functions'].append({
                    'name': func_name,
                    'code': func_code,
                    'key_features': self._extract_workflow_features(func_code)
                })
            
            elif self._is_utility_function(func_name, func_code):
                code_analysis['utility_functions'].append({
                    'name': func_name,
                    'code': func_code,
                    'purpose': self._determine_utility_purpose(func_name, func_code)
                })
            
            # Extract key code snippets
            key_snippets = self._extract_key_snippets(func_code)
            for snippet in key_snippets:
                code_analysis['code_snippets'].append({
                    'function': func_name,
                    'snippet': snippet,
                    'type': self._classify_snippet(snippet)
                })
        
        # Extract patterns
        patterns = self._extract_patterns(content)
        code_analysis['patterns'].extend(patterns)
    
    def _is_email_function(self, func_name: str, func_code: str) -> bool:
        """Check if function is email-related"""
        email_indicators = ['sendmail', 'email', 'notify', 'message', 'smtp', 'mail']
        
        func_name_lower = func_name.lower()
        func_code_lower = func_code.lower()
        
        return any(indicator in func_name_lower or indicator in func_code_lower 
                  for indicator in email_indicators)
    
    def _is_workflow_function(self, func_name: str, func_code: str) -> bool:
        """Check if function is workflow-related"""
        workflow_indicators = ['workflow', 'wtua', 'task', 'assign', 'route', 'approve']
        
        func_name_lower = func_name.lower()
        func_code_lower = func_code.lower()
        
        return any(indicator in func_name_lower or indicator in func_code_lower 
                  for indicator in workflow_indicators)
    
    def _is_utility_function(self, func_name: str, func_code: str) -> bool:
        """Check if function is a utility function"""
        utility_indicators = ['get', 'set', 'check', 'validate', 'format', 'parse', 'convert']
        
        func_name_lower = func_name.lower()
        
        return any(func_name_lower.startswith(indicator) 
                  for indicator in utility_indicators)
    
    def _extract_email_features(self, func_code: str) -> List[str]:
        """Extract key features from email functions"""
        features = []
        
        if 'aa.env.getValue' in func_code:
            features.append('Uses environment variables')
        
        if 'sendMail' in func_code:
            features.append('Uses sendMail function')
        
        if 'template' in func_code.lower():
            features.append('Uses email templates')
        
        if 'attachment' in func_code.lower():
            features.append('Supports attachments')
        
        # Extract email addresses or patterns
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        emails = re.findall(email_pattern, func_code)
        if emails:
            features.append(f'Email addresses: {", ".join(emails[:3])}')
        
        return features
    
    def _extract_workflow_features(self, func_code: str) -> List[str]:
        """Extract key features from workflow functions"""
        features = []
        
        if 'aa.workflow' in func_code:
            features.append('Uses workflow API')
        
        if 'createTask' in func_code:
            features.append('Creates workflow tasks')
        
        if 'assignTask' in func_code:
            features.append('Assigns tasks')
        
        if 'updateTask' in func_code:
            features.append('Updates task status')
        
        return features
    
    def _determine_utility_purpose(self, func_name: str, func_code: str) -> str:
        """Determine the purpose of a utility function"""
        
        if 'date' in func_name.lower() or 'date' in func_code.lower():
            return 'Date/time handling'
        elif 'format' in func_name.lower():
            return 'Data formatting'
        elif 'validate' in func_name.lower():
            return 'Data validation'
        elif 'get' in func_name.lower():
            return 'Data retrieval'
        elif 'set' in func_name.lower():
            return 'Data setting'
        else:
            return 'General utility'
    
    def _extract_key_snippets(self, func_code: str) -> List[str]:
        """Extract key code snippets from function"""
        snippets = []
        
        # Extract API calls
        api_pattern = r'aa\.[a-zA-Z]+\.[a-zA-Z]+\([^)]*\)'
        api_calls = re.findall(api_pattern, func_code)
        snippets.extend(api_calls[:5])  # Limit to 5 most important
        
        # Extract email sending code
        email_pattern = r'sendMail\([^)]*\)'
        email_calls = re.findall(email_pattern, func_code)
        snippets.extend(email_calls)
        
        # Extract conditional logic
        if_pattern = r'if\s*\([^{]*\)\s*{'
        if_statements = re.findall(if_pattern, func_code)
        snippets.extend(if_statements[:3])
        
        return snippets
    
    def _classify_snippet(self, snippet: str) -> str:
        """Classify a code snippet"""
        
        if 'aa.' in snippet:
            return 'Accela API Call'
        elif 'sendMail' in snippet:
            return 'Email Function'
        elif 'if' in snippet:
            return 'Conditional Logic'
        elif 'for' in snippet or 'while' in snippet:
            return 'Loop'
        else:
            return 'General Code'
    
    def _extract_patterns(self, content: str) -> List[Dict]:
        """Extract coding patterns from script content"""
        patterns = []
        
        # Event handler pattern
        event_pattern = r'function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*{'
        event_matches = re.findall(event_pattern, content)
        if event_matches:
            patterns.append({
                'type': 'Event Handlers',
                'count': len(event_matches),
                'examples': event_matches[:3]
            })
        
        # Error handling pattern
        try_pattern = r'try\s*{'
        try_matches = re.findall(try_pattern, content)
        if try_matches:
            patterns.append({
                'type': 'Error Handling',
                'count': len(try_matches),
                'pattern': 'try-catch blocks'
            })
        
        # Logging pattern
        log_pattern = r'logDebug|logMessage|aa\.print'
        log_matches = re.findall(log_pattern, content)
        if log_matches:
            patterns.append({
                'type': 'Logging',
                'count': len(log_matches),
                'methods': list(set(log_matches))
            })
        
        return patterns
    
    def compare_implementations(self, county1: str, county2: str, use_case: str) -> Dict[str, any]:
        """Compare implementations between two counties"""
        
        impl1 = self.get_implementation_code(county1, use_case)
        impl2 = self.get_implementation_code(county2, use_case)
        
        comparison = {
            'county1': county1,
            'county2': county2,
            'use_case': use_case,
            'differences': {
                'email_approaches': self._compare_email_approaches(impl1, impl2),
                'workflow_differences': self._compare_workflow_approaches(impl1, impl2),
                'code_complexity': self._compare_complexity(impl1, impl2),
                'unique_features': self._find_unique_features(impl1, impl2)
            },
            'similarities': self._find_similarities(impl1, impl2),
            'recommendations': self._generate_comparison_recommendations(impl1, impl2)
        }
        
        return comparison
    
    def _compare_email_approaches(self, impl1: Dict, impl2: Dict) -> Dict:
        """Compare email implementation approaches"""
        
        email1 = impl1.get('email_functions', [])
        email2 = impl2.get('email_functions', [])
        
        return {
            'county1_functions': len(email1),
            'county2_functions': len(email2),
            'county1_features': [f['key_features'] for f in email1],
            'county2_features': [f['key_features'] for f in email2],
            'approach_difference': 'Different email handling approaches' if email1 != email2 else 'Similar approaches'
        }
    
    def _compare_workflow_approaches(self, impl1: Dict, impl2: Dict) -> Dict:
        """Compare workflow implementation approaches"""
        
        workflow1 = impl1.get('workflow_functions', [])
        workflow2 = impl2.get('workflow_functions', [])
        
        return {
            'county1_workflow_functions': len(workflow1),
            'county2_workflow_functions': len(workflow2),
            'county1_features': [f['key_features'] for f in workflow1],
            'county2_features': [f['key_features'] for f in workflow2]
        }
    
    def _compare_complexity(self, impl1: Dict, impl2: Dict) -> Dict:
        """Compare code complexity"""
        
        complexity1 = len(impl1.get('code_snippets', []))
        complexity2 = len(impl2.get('code_snippets', []))
        
        return {
            'county1_complexity': complexity1,
            'county2_complexity': complexity2,
            'complexity_difference': abs(complexity1 - complexity2),
            'more_complex': impl1['county'] if complexity1 > complexity2 else impl2['county']
        }
    
    def _find_unique_features(self, impl1: Dict, impl2: Dict) -> Dict:
        """Find unique features in each implementation"""
        
        features1 = set()
        features2 = set()
        
        # Collect features from both implementations
        for func in impl1.get('email_functions', []):
            features1.update(func.get('key_features', []))
        
        for func in impl2.get('email_functions', []):
            features2.update(func.get('key_features', []))
        
        return {
            'county1_unique': list(features1 - features2),
            'county2_unique': list(features2 - features1),
            'common_features': list(features1 & features2)
        }
    
    def _find_similarities(self, impl1: Dict, impl2: Dict) -> List[str]:
        """Find similarities between implementations"""
        
        similarities = []
        
        # Check for similar function names
        names1 = {f['name'] for f in impl1.get('email_functions', [])}
        names2 = {f['name'] for f in impl2.get('email_functions', [])}
        
        common_names = names1 & names2
        if common_names:
            similarities.append(f"Common function names: {', '.join(common_names)}")
        
        # Check for similar patterns
        patterns1 = {p['type'] for p in impl1.get('patterns', [])}
        patterns2 = {p['type'] for p in impl2.get('patterns', [])}
        
        common_patterns = patterns1 & patterns2
        if common_patterns:
            similarities.append(f"Common patterns: {', '.join(common_patterns)}")
        
        return similarities
    
    def _generate_comparison_recommendations(self, impl1: Dict, impl2: Dict) -> List[str]:
        """Generate recommendations based on comparison"""
        
        recommendations = []
        
        email1_count = len(impl1.get('email_functions', []))
        email2_count = len(impl2.get('email_functions', []))
        
        if email1_count > email2_count:
            recommendations.append(f"{impl1['county']} has more comprehensive email handling")
        elif email2_count > email1_count:
            recommendations.append(f"{impl2['county']} has more comprehensive email handling")
        
        # Check complexity
        complexity1 = len(impl1.get('code_snippets', []))
        complexity2 = len(impl2.get('code_snippets', []))
        
        if complexity1 < complexity2:
            recommendations.append(f"{impl1['county']} implementation is simpler and may be easier to maintain")
        elif complexity2 < complexity1:
            recommendations.append(f"{impl2['county']} implementation is simpler and may be easier to maintain")
        
        return recommendations
