"""
Health check endpoints
"""

from fastapi import APIRouter, Request
from datetime import datetime
from ...core.logging import get_logger

router = APIRouter()
logger = get_logger("health")


@router.get("/")
async def health_check(request: Request):
    """Basic health check endpoint"""
    
    config = request.app.state.config
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0",
        "llm_enabled": config.llm_enabled,
        "service": "accela-knowledge-base"
    }


@router.get("/ready")
async def readiness_check(request: Request):
    """Readiness check - ensures system is ready to serve requests"""
    
    try:
        # Check if knowledge graph is available
        if not hasattr(request.app.state, 'knowledge_graph'):
            return {"status": "not_ready", "reason": "knowledge_graph_not_initialized"}
        
        # Check if orchestrator is available
        if not hasattr(request.app.state, 'orchestrator'):
            return {"status": "not_ready", "reason": "orchestrator_not_initialized"}
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "knowledge_graph": "initialized",
                "orchestrator": "initialized"
            }
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return {"status": "not_ready", "reason": str(e)}


@router.get("/live")
async def liveness_check():
    """Liveness check - basic service availability"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }
