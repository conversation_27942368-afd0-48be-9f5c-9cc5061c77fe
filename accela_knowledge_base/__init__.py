"""
Accela Knowledge Base - Production Architecture
A sophisticated agentic knowledge base for Accela implementations across multiple counties
"""

__version__ = "2.0.0"
__author__ = "Accela Knowledge Base Team"
__description__ = "Agentic knowledge base with graph intelligence for Accela implementations"

from .core.config import Config
from .core.exceptions import AccelaKnowledgeBaseError
from .knowledge_graph.graph_builder import AccelaKnowledgeGraph
from .agents.orchestrator import MultiAgentOrchestrator

__all__ = [
    "Config",
    "AccelaKnowledgeBaseError", 
    "AccelaKnowledgeGraph",
    "MultiAgentOrchestrator"
]
