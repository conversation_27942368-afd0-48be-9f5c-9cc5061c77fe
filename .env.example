# Accela Knowledge Base Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Data paths
ACCELA_SRC_DIRECTORY=src
ACCELA_METADATA_FILE=accela_metadata.json
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API server settings
ACCELA_API_HOST=0.0.0.0
ACCELA_API_PORT=8001
ACCELA_API_WORKERS=4
ACCELA_API_SECRET_KEY=change-this-secret-key-in-production
ACCELA_API_CORS_ORIGINS=*

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database connection (optional - uses file-based storage by default)
# ACCELA_DATABASE_URL=sqlite:///accela_kb.db
# ACCELA_DATABASE_URL=postgresql://user:password@localhost:5432/accela_kb
# ACCELA_DATABASE_URL=mysql://user:password@localhost:3306/accela_kb

# Redis cache (optional - for enhanced performance)
# ACCELA_REDIS_URL=redis://localhost:6379/0
# ACCELA_REDIS_URL=redis://user:password@localhost:6379/0

# =============================================================================
# LLM CONFIGURATION (OPTIONAL)
# =============================================================================

# OpenAI API key for enhanced intelligence (system works without this)
OPENAI_API_KEY=

# LLM model settings
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300
ACCELA_LLM_MAX_TOKENS_REASONING=150
ACCELA_LLM_TEMPERATURE=0.1

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

# Agent behavior settings
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=2
ACCELA_SIMILARITY_THRESHOLD=0.3
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.4

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Performance optimization
ACCELA_MAX_CODE_LENGTH_FOR_LLM=2000
ACCELA_MAX_SEARCH_RESULTS=20
ACCELA_CACHE_ENABLED=true

# =============================================================================
# SECURITY CONFIGURATION (OPTIONAL)
# =============================================================================

# JWT authentication (optional - for API security)
# ACCELA_JWT_SECRET_KEY=your-jwt-secret-key-here

# Rate limiting (optional - for API protection)
ACCELA_RATE_LIMIT_ENABLED=false
ACCELA_RATE_LIMIT_REQUESTS_PER_MINUTE=60

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging settings
ACCELA_LOG_LEVEL=INFO
ACCELA_LOG_FILE=logs/accela_kb.log

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development mode
ACCELA_DEBUG=false
ACCELA_RELOAD=false
ACCELA_TEST_MODE=false
