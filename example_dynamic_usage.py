#!/usr/bin/env python3
"""
Example usage of the Dynamic Query System
Demonstrates how to ask any question about county data
"""

import asyncio
import sys
import os

# Add the package to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accela_knowledge_base.core.config import Config
from accela_knowledge_base.core.dynamic_orchestrator import DynamicOrchestrator


async def example_usage():
    """Example of using the dynamic query system"""
    
    # Initialize the system
    config = Config.from_env()
    orchestrator = DynamicOrchestrator(config)
    
    print("🚀 Accela Knowledge Base - Dynamic Query System")
    print("Ask ANY question about county implementations!")
    print("=" * 60)
    
    # Example queries that work dynamically
    examples = [
        {
            "query": "Show me fee calculation differences between counties",
            "description": "Compare how different counties handle fee calculations"
        },
        {
            "query": "How do counties handle email notifications for permit approvals?",
            "description": "Analyze email notification implementations across counties"
        },
        {
            "query": "Which county has the best error handling practices?",
            "description": "Find counties with robust error handling implementations"
        },
        {
            "query": "Compare inspection scheduling workflows",
            "description": "Analyze how counties manage inspection scheduling"
        },
        {
            "query": "Show me JavaScript functions for document processing",
            "description": "Find code examples for document handling"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n📝 Example {i}: {example['description']}")
        print(f"Query: \"{example['query']}\"")
        print("-" * 50)
        
        try:
            # Process the query
            result = await orchestrator.orchestrate(example['query'])
            
            # Show key results
            print(f"✅ Successfully processed!")
            print(f"   Intent: {result.query_analysis.intent}")
            print(f"   Analysis Type: {result.query_analysis.analysis_type}")
            print(f"   Counties Analyzed: {len(result.county_results)}")
            print(f"   Confidence: {result.confidence:.1%}")
            print(f"   Processing Time: {result.processing_time:.2f}s")
            
            # Show a snippet of the response
            response_preview = result.markdown_response[:200].replace('\n', ' ')
            print(f"   Response Preview: {response_preview}...")
            
        except Exception as e:
            print(f"❌ Error: {e}")


async def interactive_demo():
    """Interactive demo where you can ask your own questions"""
    
    config = Config.from_env()
    orchestrator = DynamicOrchestrator(config)
    
    print("\n🎯 Interactive Demo")
    print("=" * 30)
    print("Ask your own questions about county implementations!")
    print("Type 'quit' to exit")
    
    while True:
        try:
            query = input("\n💬 Your question: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            print("🔄 Processing...")
            
            # Process the query
            result = await orchestrator.orchestrate(query)
            
            print(f"\n📊 Analysis Results:")
            print(f"   Intent: {result.query_analysis.intent}")
            print(f"   Type: {result.query_analysis.analysis_type}")
            print(f"   Counties: {[r.county for r in result.county_results]}")
            print(f"   Confidence: {result.confidence:.1%}")
            
            # Show the full response
            print(f"\n📝 Response:")
            print("-" * 40)
            print(result.markdown_response)
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


async def api_example():
    """Example of how to use the API endpoints"""
    
    print("\n🌐 API Usage Examples")
    print("=" * 30)
    
    print("1. Dynamic Query Endpoint:")
    print("   POST /agentic/dynamic")
    print("   {")
    print('     "query": "Show me fee calculation differences between counties",')
    print('     "counties": ["asheville", "santa_barbara"]  // optional')
    print("   }")
    
    print("\n2. Simple Ask Endpoint:")
    print("   POST /agentic/ask")
    print("   {")
    print('     "query": "How do counties handle permit workflows?",')
    print('     "counties": "asheville, santa_barbara"  // optional, comma-separated')
    print("   }")
    
    print("\n3. cURL Examples:")
    print("   # Dynamic query")
    print("   curl -X POST 'http://localhost:8001/agentic/dynamic' \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"query\": \"Compare fee calculations between counties\"}'")
    
    print("\n   # Simple ask")
    print("   curl -X POST 'http://localhost:8001/agentic/ask' \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"query\": \"Show me inspection workflows\"}'")


if __name__ == "__main__":
    print("🧪 Dynamic Query System Examples")
    
    # Run examples
    asyncio.run(example_usage())
    
    # Show API examples
    asyncio.run(api_example())
    
    # Interactive demo (uncomment to try)
    # asyncio.run(interactive_demo())
