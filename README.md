# Accela Knowledge Base for LLM Integration

A comprehensive knowledge base system for multiple county Accela implementations, designed for seamless LLM integration and intelligent code assistance.

## 🎯 Overview

This system creates an **intelligent, agentic knowledge base** from your collection of Accela implementations across different counties, featuring:

- **🤖 Multi-Agent Intelligence**: Specialized agents for analysis, comparison, recommendation, and synthesis
- **🕸️ Enhanced Graph Knowledge**: Network-based understanding with **Accela naming convention parsing**
- **🏷️ Accela Convention Analysis**: Deep parsing of `aaaa;b!c!d!e` naming patterns (event prefixes, modules, app types)
- **🎯 Context-Aware Recommendations**: AI agents use event prefixes and naming context for optimal suggestions
- **📊 Cross-County Analysis**: Compare implementations using Accela-specific metadata and patterns
- **🔄 Intelligent Synthesis**: Automatic combination leveraging naming convention insights
- **🚀 LLM Integration**: Ready-to-use API with Accela domain expertise
- **📈 Naming Convention Insights**: Analyze event prefix usage, module coverage, and naming patterns

## 🏗️ Agentic Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Source Code   │───▶│  Data Extraction │───▶│ Knowledge Graph │
│  (9 Counties)   │    │   & Analysis     │    │ + Vector Store  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                       ┌─────────────────────────────────┘
                       │
                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    INTELLIGENT AGENT LAYER                      │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│  Analyzer   │ Comparator  │R<PERSON>ommender  │    Synthesizer      │
│   Agent     │   Agent     │   Agent     │      Agent          │
│             │             │             │                     │
│ • Patterns  │ • County    │ • Best      │ • Optimal Solution  │
│ • Functions │   Analysis  │   Practices │ • Implementation    │
│ • Quality   │ • Gaps      │ • Scoring   │   Plan              │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
                       │
                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   LLM/Chatbot   │◀───│ Orchestration    │───▶│  Agentic API    │
│   Integration   │    │     Engine       │    │   (FastAPI)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📊 Current Implementation Coverage

Your knowledge base includes **9 county/agency implementations**:

| County/Agency | Repository | Script Types | Status |
|---------------|------------|--------------|--------|
| Asheville | accela-masterscripts | Event, Batch, Interface, Set | ✅ |
| Santa Barbara | Accela | Event, Batch, Pageflow | ✅ |
| Dayton | AccelaProd | Scripts, Custom Includes | ✅ |
| Leon County | LeonAccelaScripts | Scripts, Documentation | ✅ |
| Atlanta/Chattanooga | MyAccela | Multiple cities | ✅ |
| Lancaster | AccelaSupp | Event, Batch, Expression | ✅ |
| COK Applications | COKApplications-Accela | Scripts | ✅ |
| EP App Support | AccelaDEV | Development scripts | ✅ |
| Solano County | EMSE_DEV_3_0 | EMSE environment | ✅ |

## 🧠 Dynamic Query System (NEW!)

The Accela Knowledge Base now features a **completely dynamic query system** that can handle ANY question about county data without hardcoded patterns or use cases. Using LLM intelligence, it understands your intent and provides comprehensive analysis.

### Key Features

- **🎯 Universal Query Understanding**: Ask any question about county implementations
- **🔍 Intelligent Analysis**: LLM-powered analysis of county data and code
- **⚖️ Dynamic Comparison**: Automatic comparison when multiple counties are relevant
- **📝 Contextual Responses**: Comprehensive markdown responses tailored to your question
- **🚀 No Hardcoding**: Completely flexible - works with any type of query

### Example Queries

```bash
# Fee-related questions
"Show me fee calculation differences between counties"
"How do different counties handle permit fees?"
"Which county has the most efficient fee processing?"

# Workflow questions
"Compare inspection workflows across counties"
"How do counties handle permit approval processes?"
"What are the differences in application review procedures?"

# Technical implementation
"Show me email notification implementations"
"How do counties integrate with external systems?"
"What JavaScript functions are used for permit processing?"

# General analysis
"Which county has the most comprehensive implementation?"
"What are common patterns across all counties?"
"How do counties handle error scenarios?"
```

### API Endpoints

#### Dynamic Query Endpoint
```bash
POST /agentic/dynamic
Content-Type: application/json

{
  "query": "Show me fee calculation differences between counties",
  "counties": ["asheville", "santa_barbara"]  // optional
}
```

#### Simple Ask Endpoint
```bash
POST /agentic/ask
Content-Type: application/json

{
  "query": "How do counties handle permit workflows?",
  "counties": "asheville, santa_barbara"  // optional, comma-separated
}
```

### Response Format

The system returns comprehensive HTML responses with:
- **Executive Summary**: Direct answer to your question
- **Detailed Findings**: Analysis for each county
- **Code Examples**: Relevant code snippets with explanations
- **Comparison Analysis**: Similarities and differences (when applicable)
- **Best Practices**: Identified optimal approaches
- **Recommendations**: Actionable next steps

## 🚀 Quick Start

### 1. Setup (One-time)

```bash
# Run complete production setup (this will take 5-10 minutes)
chmod +x setup_production.sh
./setup_production.sh
```

This script will:
- Create Python virtual environment
- Install all dependencies (including optional OpenAI)
- Setup environment configuration (.env files)
- Extract metadata from all Accela scripts
- Build knowledge graph with Accela naming convention
- Test the production system
- Validate environment configuration

### 2. Configuration

The system uses environment variables for configuration:

```bash
# Initialize .env file from example
accela-kb env init

# Check environment status
accela-kb env status

# Validate configuration
accela-kb env validate
```

**Optional LLM Enhancement:**
```bash
# Edit .env file and uncomment/set:
OPENAI_API_KEY=your-openai-api-key-here

# Or export as environment variable
export OPENAI_API_KEY="your-openai-api-key-here"
```

### 2. Start the API Server

```bash
# Activate virtual environment
source venv/bin/activate

# Start the API server
python knowledge_base_api.py
```

The API will be available at:
- **Main API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔍 Agentic API Usage Examples

### Intelligent Query with Multi-Agent Analysis
```bash
curl -X POST 'http://localhost:8001/agentic/query' \
     -H 'Content-Type: application/json' \
     -d '{
       "query": "email notification when permit is issued",
       "use_case": "permit_notification",
       "target_counties": ["asheville", "santa_barbara", "dayton"],
       "constraints": {"complexity": "low", "documentation": "good"}
     }'
```

### Advanced County Comparison
```bash
curl -X POST 'http://localhost:8001/agentic/compare' \
     -H 'Content-Type: application/json' \
     -d '{
       "use_case": "permit_workflow",
       "counties": ["asheville", "santa_barbara", "dayton"],
       "criteria": {"complexity": 0.3, "doc_quality": 0.4, "usage_frequency": 0.3}
     }'
```

### Best Practice Identification
```bash
curl -X POST 'http://localhost:8001/agentic/best-practices' \
     -H 'Content-Type: application/json' \
     -d '{
       "domain": "permits",
       "min_counties": 2,
       "quality_threshold": 0.7
     }'
```

### Implementation Plan Generation
```bash
curl -X POST 'http://localhost:8001/agentic/implementation-plan' \
     -H 'Content-Type: application/json' \
     -d '{
       "selected_implementation": {
         "county": "asheville",
         "metadata": {"functions": ["emailContact", "addCondition", "validateAddress"]}
       },
       "target_environment": "production",
       "timeline_weeks": 4
     }'
```

### Accela Event Prefix Analysis
```bash
curl 'http://localhost:8001/accela/event-prefix-analysis'
```

### Module vs Application Type Matrix
```bash
curl 'http://localhost:8001/accela/module-matrix'
```

### Accela Naming Convention Insights
```bash
# All counties
curl 'http://localhost:8001/accela/naming-insights'

# Specific county
curl 'http://localhost:8001/accela/naming-insights?county=asheville'
```

### Knowledge Graph Statistics
```bash
curl 'http://localhost:8001/graph/stats'
```

## 🤖 Agentic LLM Integration

### Python Client Example

```python
import asyncio
from accela_knowledge_base.agents import MultiAgentOrchestrator
from accela_knowledge_base.core.models import OrchestrationRequest
from accela_knowledge_base.knowledge_graph.graph_builder import AccelaKnowledgeGraph
from accela_knowledge_base.core.config import Config

# Initialize system
config = Config.from_env()
knowledge_graph = AccelaKnowledgeGraph(config)
knowledge_graph.build_from_metadata()
orchestrator = MultiAgentOrchestrator(knowledge_graph, config)

# Create intelligent request
async def get_best_implementation():
    request = OrchestrationRequest(
        request_id="unique_id",
        query="email notification when permit is issued",
        use_case="permit_notification",
        target_counties=["asheville", "santa_barbara"],
        constraints={"complexity": "low"}
    )

    # Get multi-agent analysis
    result = await orchestrator.orchestrate(request)

    print(f"Best Implementation: {result.best_implementation['county']}")
    print(f"Confidence: {result.confidence:.2f}")
    print(f"Recommendations: {result.recommendations}")

    return result

# Run the analysis
result = asyncio.run(get_best_implementation())
```

### Agentic Chatbot Integration

```python
import requests

class AgenticAccelaChatbot:
    def __init__(self):
        self.api_base = "http://localhost:8001"

    def process_message(self, message: str, use_case: str = "general") -> str:
        # Use agentic query for intelligent responses
        response = requests.post(f"{self.api_base}/agentic/query", json={
            "query": message,
            "use_case": use_case,
            "target_counties": ["asheville", "santa_barbara", "dayton"]
        })

        if response.status_code == 200:
            result = response.json()
            return f"""
Best Implementation: {result['best_implementation']['county']}
Confidence: {result['confidence']:.2f}
Key Recommendations:
{chr(10).join(f"• {rec}" for rec in result['recommendations'][:3])}
            """
        else:
            return "Sorry, I couldn't process your request."

# Usage
chatbot = AgenticAccelaChatbot()
response = chatbot.process_message(
    "How do I implement permit notifications?",
    "permit_notification"
)
print(response)
```

## 📁 File Structure

```
accela/
├── src/                          # Source Accela implementations (external repos - not tracked)
├── accela_knowledge_base/        # Main Python package
│   ├── agents/                   # Multi-agent orchestration system
│   ├── api/                      # FastAPI REST endpoints
│   ├── cli/                      # Command-line interface
│   ├── core/                     # Core functionality and models
│   ├── data/                     # Data processing and extraction
│   ├── knowledge_graph/          # Graph building and analysis
│   ├── llm/                      # LLM integration helpers
│   └── tests/                    # Test suite
├── main.py                       # Main entry point
├── requirements.txt              # Python dependencies
├── setup.py                      # Package setup
├── .env.example                  # Environment configuration template
├── install_repos.sh              # Repository installation script
├── setup_production.sh           # Production setup script
├── accela_metadata.json          # Generated script metadata (ignored)
└── accela_knowledge_graph.gexf   # Generated knowledge graph (ignored)
```

## 🛠️ Advanced Configuration

### Custom Agent Configuration

```python
# Configure orchestrator
from accela_knowledge_base.agents import MultiAgentOrchestrator

orchestrator = MultiAgentOrchestrator(knowledge_graph, config)

# Customize recommendation criteria
custom_criteria = {
    'complexity': 0.2,      # Lower weight for complexity
    'doc_quality': 0.5,     # Higher weight for documentation
    'usage_frequency': 0.3   # Standard weight for usage
}

# Use in orchestration request
request = OrchestrationRequest(
    query="your query",
    use_case="your_use_case",
    constraints={"criteria": custom_criteria}
)
```

### Production Deployment

```bash
# Install production dependencies
pip install gunicorn

# Run agentic API with Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker agentic_api_endpoints:app --bind 0.0.0.0:8001
```

### LLM Intelligence (Optional)

```bash
# Enable enhanced LLM intelligence in .env file
OPENAI_API_KEY=your-openai-api-key-here

# System works perfectly without LLM - only adds semantic analysis
```

**LLM Benefits:**
- Enhanced code semantic analysis
- Intelligent implementation reasoning
- Only used where genuinely beneficial
- Graceful degradation without LLM

### Graph Visualization

```python
# Export graph for visualization tools like Gephi
knowledge_graph.export_graph("accela_graph.gexf")

# View graph statistics
stats = knowledge_graph.get_graph_stats()
print(f"Graph density: {stats['density']:.3f}")
print(f"Average clustering: {stats['avg_clustering']:.3f}")
```

## 📈 Performance & Scaling

- **Agent Response Time**: ~200ms for single agent, ~800ms for full orchestration
- **Graph Size**: ~50MB for knowledge graph with 9 counties
- **Memory Usage**: ~300MB RAM for graph + agents
- **Concurrent Users**: 20+ with default setup (agents are CPU-intensive)

For larger deployments:
- Deploy agents as separate microservices
- Use Redis for agent memory caching
- Implement agent result caching for common queries
- Scale horizontally with load balancer

## 🔄 Keeping Updated

### Manual Update
```bash
# Re-run the extraction and graph building
accela-kb extract-metadata
accela-kb build-graph
```

### Automated Updates (Future Enhancement)
- Git webhook integration for real-time updates
- Incremental graph updates when repositories change
- Agent learning from usage patterns
- Automated best practice discovery

## 🎯 Agentic Use Cases

1. **Intelligent Development Assistance**
   - "Find the best way to implement permit notifications across all counties"
   - "What's the optimal approach for inspection scheduling based on existing implementations?"
   - "Synthesize the best address validation solution from multiple counties"

2. **Automated Best Practice Discovery**
   - Multi-agent analysis identifies patterns automatically
   - Cross-county comparison reveals optimization opportunities
   - Intelligent synthesis creates hybrid solutions

3. **Strategic Planning & Standardization**
   - Graph analysis reveals implementation gaps
   - Agent recommendations guide standardization efforts
   - Automated implementation planning with risk assessment

4. **Knowledge Transfer & Training**
   - Agents generate contextual training materials
   - Intelligent onboarding for new developers
   - Automated documentation of best practices

## 🤝 Contributing

1. Add new county implementations to `src/`
2. Run `accela-kb extract-metadata` and `accela-kb build-graph` to update
3. Test with `accela-kb query "your test query"`
4. Verify API with `accela-kb serve` and visit http://localhost:8001/docs

## 📞 Support

For questions or issues:
1. Check the API docs at http://localhost:8001/docs
2. Use the CLI: `accela-kb --help` for available commands
3. Test queries with: `accela-kb query "your question"`
4. Examine graph relationships with exported visualization files

---

**Ready to revolutionize your Accela development with intelligent agents!** 🤖🚀
