#!/bin/bash

# Create src directory if it doesn't exist
mkdir -p src

# Clone all repositories
cd src

git clone https://github.com/CityofDaytonIT/AccelaProd.git
git clone https://github.com/CityofSantaBarbara/Accela.git
git clone https://github.com/cityofasheville/accela-masterscripts.git
git clone https://github.com/grayquarter/LeonAccelaScripts.git
git clone https://github.com/joeybullock/MyAccela.git
git clone https://github.com/COKApplications/Accela.git COKApplications-Accela
git clone https://github.com/EPAppSupport/AccelaDEV.git
git clone https://github.com/LancasterCA/AccelaSupp.git
git clone https://github.com/kirstenCG/EMSE_DEV_3_0.git

echo "All repositories cloned successfully!"